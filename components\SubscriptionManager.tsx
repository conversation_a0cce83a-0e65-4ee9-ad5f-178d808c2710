'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger 
} from '@/components/ui/dropdown-menu'
import { MoreVertical, CreditCard, Pause, Play, X, ExternalLink } from 'lucide-react'
import { useSession } from 'next-auth/react'
import { toast } from 'sonner'

interface Subscription {
  id: string
  name: string
  status: string
  statusFormatted: string
  price: string
  renewsAt: string
  endsAt: string
  isPaused: boolean
  planName: string
}

export default function SubscriptionManager() {
  const { data: session } = useSession()
  const [subscriptions, setSubscriptions] = useState<Subscription[]>([])
  const [loading, setLoading] = useState(true)
  const [actionLoading, setActionLoading] = useState<string | null>(null)

  useEffect(() => {
    if (session?.user) {
      fetchSubscriptions()
    }
  }, [session])

  const fetchSubscriptions = async () => {
    try {
      const response = await fetch('/api/subscriptions')
      if (response.ok) {
        const data = await response.json()
        setSubscriptions(data.subscriptions || [])
      }
    } catch (error) {
      console.error('Error fetching subscriptions:', error)
      toast.error('Failed to load subscriptions')
    } finally {
      setLoading(false)
    }
  }

  const handleSubscriptionAction = async (subscriptionId: string, action: string) => {
    setActionLoading(subscriptionId)
    
    try {
      const response = await fetch('/api/subscriptions/manage', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          subscriptionId,
          action,
        }),
      })

      if (response.ok) {
        toast.success(`Subscription ${action}d successfully`)
        await fetchSubscriptions()
      } else {
        throw new Error(`Failed to ${action} subscription`)
      }
    } catch (error) {
      console.error(`Error ${action}ing subscription:`, error)
      toast.error(`Failed to ${action} subscription`)
    } finally {
      setActionLoading(null)
    }
  }

  const openCustomerPortal = async (subscriptionId: string) => {
    try {
      const response = await fetch(`/api/subscriptions/${subscriptionId}/portal`)
      if (response.ok) {
        const { portalUrl } = await response.json()
        window.open(portalUrl, '_blank')
      } else {
        throw new Error('Failed to get portal URL')
      }
    } catch (error) {
      console.error('Error opening customer portal:', error)
      toast.error('Failed to open customer portal')
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
      case 'paused':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300'
      case 'cancelled':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
      case 'expired':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300'
      default:
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300'
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    })
  }

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Subscription Management</CardTitle>
          <CardDescription>Loading your subscription details...</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="animate-pulse space-y-4">
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4"></div>
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (subscriptions.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Subscription Management</CardTitle>
          <CardDescription>You don't have any active subscriptions</CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-gray-600 dark:text-gray-300 mb-4">
            Subscribe to a plan to unlock premium features and enhanced text humanization capabilities.
          </p>
          <Button onClick={() => window.location.href = '/pricing'}>
            View Plans
          </Button>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
          Subscription Management
        </h2>
        <p className="text-gray-600 dark:text-gray-300">
          Manage your active subscriptions and billing details
        </p>
      </div>

      {subscriptions.map((subscription) => (
        <Card key={subscription.id}>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="flex items-center gap-2">
                  {subscription.planName}
                  <Badge className={getStatusColor(subscription.status)}>
                    {subscription.statusFormatted}
                  </Badge>
                </CardTitle>
                <CardDescription>
                  ${subscription.price}/month • 
                  {subscription.status === 'active' && subscription.renewsAt && (
                    <span> Renews on {formatDate(subscription.renewsAt)}</span>
                  )}
                  {subscription.status === 'cancelled' && subscription.endsAt && (
                    <span> Ends on {formatDate(subscription.endsAt)}</span>
                  )}
                </CardDescription>
              </div>

              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button 
                    variant="ghost" 
                    size="sm"
                    disabled={actionLoading === subscription.id}
                  >
                    <MoreVertical className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={() => openCustomerPortal(subscription.id)}>
                    <ExternalLink className="mr-2 h-4 w-4" />
                    Customer Portal
                  </DropdownMenuItem>
                  
                  {subscription.status === 'active' && !subscription.isPaused && (
                    <DropdownMenuItem 
                      onClick={() => handleSubscriptionAction(subscription.id, 'pause')}
                    >
                      <Pause className="mr-2 h-4 w-4" />
                      Pause Subscription
                    </DropdownMenuItem>
                  )}
                  
                  {subscription.isPaused && (
                    <DropdownMenuItem 
                      onClick={() => handleSubscriptionAction(subscription.id, 'unpause')}
                    >
                      <Play className="mr-2 h-4 w-4" />
                      Resume Subscription
                    </DropdownMenuItem>
                  )}
                  
                  {subscription.status === 'active' && (
                    <DropdownMenuItem 
                      onClick={() => {
                        if (confirm('Are you sure you want to cancel your subscription?')) {
                          handleSubscriptionAction(subscription.id, 'cancel')
                        }
                      }}
                      className="text-red-600 dark:text-red-400"
                    >
                      <X className="mr-2 h-4 w-4" />
                      Cancel Subscription
                    </DropdownMenuItem>
                  )}
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </CardHeader>
        </Card>
      ))}
    </div>
  )
}
