'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Check, Loader2 } from 'lucide-react'
import { useSession } from 'next-auth/react'
import { toast } from 'sonner'

interface Plan {
  id: string
  name: string
  description: string
  price: string
  interval: string
  features: string[]
  variantId: number
  isPopular?: boolean
}

const plans: Plan[] = [
  {
    id: 'basic',
    name: 'Basic',
    description: 'Perfect for individuals getting started',
    price: '$9',
    interval: 'month',
    variantId: 0, // Replace with actual variant ID from LemonSqueezy
    features: [
      'Up to 10,000 words per month',
      'Basic humanization',
      'Standard processing speed',
      'Email support'
    ]
  },
  {
    id: 'pro',
    name: 'Pro',
    description: 'Best for professionals and content creators',
    price: '$29',
    interval: 'month',
    variantId: 0, // Replace with actual variant ID from LemonSqueezy
    isPopular: true,
    features: [
      'Up to 100,000 words per month',
      'Advanced humanization',
      'Priority processing',
      'Batch file processing',
      'API access',
      'Priority support'
    ]
  },
  {
    id: 'enterprise',
    name: 'Enterprise',
    description: 'For teams and large organizations',
    price: '$99',
    interval: 'month',
    variantId: 0, // Replace with actual variant ID from LemonSqueezy
    features: [
      'Unlimited words',
      'Heavy humanization mode',
      'Fastest processing',
      'Advanced batch processing',
      'Full API access',
      'Custom integrations',
      'Dedicated support',
      'Team management'
    ]
  }
]

export default function SubscriptionPlans() {
  const { data: session } = useSession()
  const [loading, setLoading] = useState<string | null>(null)

  const handleSubscribe = async (plan: Plan) => {
    if (!session?.user) {
      toast.error('Please sign in to subscribe')
      return
    }

    setLoading(plan.id)

    try {
      const response = await fetch('/api/checkout', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          variantId: plan.variantId,
          embed: true
        }),
      })

      if (!response.ok) {
        throw new Error('Failed to create checkout')
      }

      const { checkoutUrl } = await response.json()

      // Open LemonSqueezy checkout
      if (typeof window !== 'undefined' && window.LemonSqueezy) {
        window.LemonSqueezy.Url.Open(checkoutUrl)
      } else {
        // Fallback to redirect
        window.location.href = checkoutUrl
      }
    } catch (error) {
      console.error('Subscription error:', error)
      toast.error('Failed to start subscription process')
    } finally {
      setLoading(null)
    }
  }

  // Load LemonSqueezy script
  useEffect(() => {
    const script = document.createElement('script')
    script.src = 'https://app.lemonsqueezy.com/js/lemon.js'
    script.defer = true
    document.head.appendChild(script)

    script.onload = () => {
      if (typeof window.createLemonSqueezy === 'function') {
        window.createLemonSqueezy()
      }
    }

    return () => {
      document.head.removeChild(script)
    }
  }, [])

  return (
    <div className="py-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
            Choose Your Plan
          </h2>
          <p className="text-lg text-gray-600 dark:text-gray-300">
            Unlock the full potential of AI text humanization
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {plans.map((plan) => (
            <Card 
              key={plan.id} 
              className={`relative ${
                plan.isPopular 
                  ? 'border-blue-500 shadow-lg scale-105' 
                  : 'border-gray-200 dark:border-gray-700'
              }`}
            >
              {plan.isPopular && (
                <Badge className="absolute -top-3 left-1/2 transform -translate-x-1/2 bg-blue-500">
                  Most Popular
                </Badge>
              )}
              
              <CardHeader className="text-center">
                <CardTitle className="text-2xl font-bold">{plan.name}</CardTitle>
                <CardDescription className="text-gray-600 dark:text-gray-300">
                  {plan.description}
                </CardDescription>
                <div className="mt-4">
                  <span className="text-4xl font-bold text-gray-900 dark:text-white">
                    {plan.price}
                  </span>
                  <span className="text-gray-600 dark:text-gray-300">
                    /{plan.interval}
                  </span>
                </div>
              </CardHeader>

              <CardContent>
                <ul className="space-y-3 mb-6">
                  {plan.features.map((feature, index) => (
                    <li key={index} className="flex items-center">
                      <Check className="h-5 w-5 text-green-500 mr-3 flex-shrink-0" />
                      <span className="text-gray-700 dark:text-gray-300">{feature}</span>
                    </li>
                  ))}
                </ul>

                <Button
                  onClick={() => handleSubscribe(plan)}
                  disabled={loading === plan.id}
                  className={`w-full ${
                    plan.isPopular
                      ? 'bg-blue-600 hover:bg-blue-700'
                      : 'bg-gray-900 hover:bg-gray-800 dark:bg-white dark:text-gray-900 dark:hover:bg-gray-100'
                  }`}
                >
                  {loading === plan.id ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Processing...
                    </>
                  ) : (
                    'Get Started'
                  )}
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </div>
  )
}

// Extend Window interface for LemonSqueezy
declare global {
  interface Window {
    LemonSqueezy: {
      Url: {
        Open: (url: string) => void
      }
    }
    createLemonSqueezy: () => void
  }
}
