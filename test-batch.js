// Simple test for batch processing functionality
const fs = require('fs');
const path = require('path');

// Create test files
const testFiles = [
  {
    name: 'test1.txt',
    content: 'This is AI-generated content that needs to be humanized. The artificial intelligence system has produced this text with typical AI patterns and structures.'
  },
  {
    name: 'test2.txt', 
    content: 'Another example of AI text that requires humanization processing. This content exhibits common artificial intelligence writing characteristics.'
  },
  {
    name: 'test3.md',
    content: '# AI Content\n\nThis markdown file contains AI-generated content that should be processed through the humanization system.'
  }
];

// Create test files
testFiles.forEach(file => {
  fs.writeFileSync(path.join(__dirname, file.name), file.content);
  console.log(`Created test file: ${file.name}`);
});

console.log('Test files created successfully!');
console.log('You can now test the batch processing functionality by uploading these files.');
