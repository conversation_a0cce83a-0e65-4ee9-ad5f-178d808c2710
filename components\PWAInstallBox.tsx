'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { X, Download, Smartphone } from 'lucide-react';

interface PWAInstallBoxProps {
  className?: string;
}

export default function PWAInstallBox({ className = '' }: PWAInstallBoxProps) {
  const [deferredPrompt, setDeferredPrompt] = useState<any>(null);
  const [showInstallBox, setShowInstallBox] = useState(false);
  const [isInstalled, setIsInstalled] = useState(false);

  useEffect(() => {
    // Check if already installed
    if (window.matchMedia && window.matchMedia('(display-mode: standalone)').matches) {
      setIsInstalled(true);
      return;
    }

    // Check if banner was dismissed recently (within 7 days)
    const dismissedTime = localStorage.getItem('pwa-banner-dismissed');
    const sevenDaysInMs = 7 * 24 * 60 * 60 * 1000;

    if (dismissedTime && (Date.now() - parseInt(dismissedTime)) < sevenDaysInMs) {
      return;
    }

    // Listen for the beforeinstallprompt event
    const handleBeforeInstallPrompt = (e: Event) => {
      e.preventDefault();
      setDeferredPrompt(e);
      setShowInstallBox(true);
    };

    // Listen for app installed event
    const handleAppInstalled = () => {
      setIsInstalled(true);
      setShowInstallBox(false);
      setDeferredPrompt(null);
      
      // Track installation
      if (typeof gtag !== 'undefined') {
        gtag('event', 'pwa_installed', {
          event_category: 'engagement',
          event_label: 'PWA Installation'
        });
      }
    };

    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
    window.addEventListener('appinstalled', handleAppInstalled);

    return () => {
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
      window.removeEventListener('appinstalled', handleAppInstalled);
    };
  }, []);

  const handleInstall = async () => {
    if (!deferredPrompt) return;

    try {
      deferredPrompt.prompt();
      const { outcome } = await deferredPrompt.userChoice;
      
      if (outcome === 'accepted') {
        console.log('User accepted the install prompt');
        // Track installation attempt
        if (typeof gtag !== 'undefined') {
          gtag('event', 'pwa_install_accepted', {
            event_category: 'engagement',
            event_label: 'PWA Install Prompt Accepted'
          });
        }
      } else {
        console.log('User dismissed the install prompt');
        handleDismiss();
      }
      
      setDeferredPrompt(null);
    } catch (error) {
      console.error('Error during PWA installation:', error);
    }
  };

  const handleDismiss = () => {
    setShowInstallBox(false);
    
    // Store dismissal in localStorage to prevent showing again for 7 days
    localStorage.setItem('pwa-banner-dismissed', Date.now().toString());
    
    // Track dismissal
    if (typeof gtag !== 'undefined') {
      gtag('event', 'pwa_banner_dismissed', {
        event_category: 'engagement',
        event_label: 'PWA Banner Manually Dismissed'
      });
    }
  };

  // Don't show if already installed or conditions not met
  if (isInstalled || !showInstallBox || !deferredPrompt) {
    return null;
  }

  return (
    <Card className={`bg-gradient-to-br from-slate-800/80 to-slate-700/80 backdrop-blur-lg border-slate-600/50 ${className}`}>
      <CardContent className="p-4">
        <div className="flex items-start justify-between gap-3">
          <div className="flex items-center gap-3 flex-1 min-w-0">
            <div className="flex-shrink-0">
              <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                <Smartphone className="w-5 h-5 text-white" />
              </div>
            </div>
            <div className="flex-1 min-w-0">
              <h4 className="text-sm font-semibold text-white mb-1">
                Install GhostLayer App
              </h4>
              <p className="text-xs text-gray-300 mb-3 leading-relaxed">
                Get quick access, offline support, and enhanced performance
              </p>
              <div className="flex gap-2">
                <Button
                  onClick={handleInstall}
                  size="sm"
                  className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white text-xs px-3 py-1 h-7"
                >
                  <Download className="w-3 h-3 mr-1" />
                  Install
                </Button>
                <Button
                  onClick={handleDismiss}
                  variant="ghost"
                  size="sm"
                  className="text-gray-400 hover:text-white text-xs px-2 py-1 h-7"
                >
                  Later
                </Button>
              </div>
            </div>
          </div>
          <Button
            onClick={handleDismiss}
            variant="ghost"
            size="sm"
            className="text-gray-400 hover:text-white p-1 h-6 w-6 flex-shrink-0"
          >
            <X className="w-3 h-3" />
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
