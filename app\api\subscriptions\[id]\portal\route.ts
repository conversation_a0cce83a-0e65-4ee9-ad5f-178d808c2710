import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth/next'
import { getSubscription } from '@lemonsqueezy/lemonsqueezy.js'
import { configureLemonSqueezy } from '@/lib/lemonsqueezy'
import { authOptions } from '@/app/api/auth/[...nextauth]/route'

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user) {
      return NextResponse.json(
        { error: 'User is not authenticated' },
        { status: 401 }
      )
    }

    const subscriptionId = params.id

    if (!subscriptionId) {
      return NextResponse.json(
        { error: 'Subscription ID is required' },
        { status: 400 }
      )
    }

    // Configure LemonSqueezy SDK
    configureLemonSqueezy()

    // Get subscription details including URLs
    const subscription = await getSubscription(subscriptionId)

    if (subscription.error) {
      console.error('LemonSqueezy subscription error:', subscription.error)
      return NextResponse.json(
        { error: 'Failed to get subscription details' },
        { status: 500 }
      )
    }

    const urls = subscription.data?.data.attributes.urls

    return NextResponse.json({
      portalUrl: urls?.customer_portal,
      updatePaymentUrl: urls?.update_payment_method,
    })
  } catch (error) {
    console.error('Customer portal error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
