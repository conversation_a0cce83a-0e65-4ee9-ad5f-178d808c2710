import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth/next'
import { authOptions } from '@/app/api/auth/[...nextauth]/route'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user) {
      return NextResponse.json(
        { error: 'User is not authenticated' },
        { status: 401 }
      )
    }

    // Here you would typically fetch subscriptions from your database
    // For now, we'll return mock data
    const mockSubscriptions = [
      {
        id: 'sub_123',
        name: 'Pro Plan',
        status: 'active',
        statusFormatted: 'Active',
        price: '29.00',
        renewsAt: '2024-02-28T00:00:00Z',
        endsAt: null,
        isPaused: false,
        planName: 'GhostLayer Pro',
      }
    ]

    return NextResponse.json({
      subscriptions: mockSubscriptions,
    })
  } catch (error) {
    console.error('Error fetching subscriptions:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
