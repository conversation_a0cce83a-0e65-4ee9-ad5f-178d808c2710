import { Metadata } from 'next'
import SubscriptionPlans from '@/components/SubscriptionPlans'

export const metadata: Metadata = {
  title: 'Pricing - GhostLayer',
  description: 'Choose the perfect plan for your AI text humanization needs. Flexible pricing for individuals, professionals, and enterprises.',
}

export default function PricingPage() {
  return (
    <div className="min-h-screen bg-white dark:bg-gray-900">
      {/* Header */}
      <div className="bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-6">
            Simple, Transparent Pricing
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            Transform AI-generated text into natural, human-like content with our advanced humanization technology. 
            Choose the plan that fits your needs.
          </p>
        </div>
      </div>

      {/* Pricing Plans */}
      <SubscriptionPlans />

      {/* FAQ Section */}
      <div className="py-16 bg-gray-50 dark:bg-gray-800">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-bold text-center text-gray-900 dark:text-white mb-12">
            Frequently Asked Questions
          </h2>
          
          <div className="space-y-8">
            <div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                What is text humanization?
              </h3>
              <p className="text-gray-600 dark:text-gray-300">
                Text humanization is the process of transforming AI-generated content to make it sound more natural, 
                engaging, and human-like. Our advanced algorithms analyze and modify text patterns, sentence structures, 
                and word choices to bypass AI detection systems while maintaining the original meaning.
              </p>
            </div>

            <div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                Can I change my plan anytime?
              </h3>
              <p className="text-gray-600 dark:text-gray-300">
                Yes, you can upgrade or downgrade your plan at any time. Changes will be prorated and reflected 
                in your next billing cycle. You can manage your subscription through your dashboard.
              </p>
            </div>

            <div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                What file formats do you support?
              </h3>
              <p className="text-gray-600 dark:text-gray-300">
                We support a wide range of file formats including .txt, .md, .docx, .pdf, .rtf, .odt, .json, 
                .csv, .html, and .xml. Pro and Enterprise plans include batch processing for multiple files.
              </p>
            </div>

            <div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                Is there a free trial?
              </h3>
              <p className="text-gray-600 dark:text-gray-300">
                Yes, all new users get access to basic humanization features with limited monthly usage. 
                You can try our service before committing to a paid plan.
              </p>
            </div>

            <div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                How does billing work?
              </h3>
              <p className="text-gray-600 dark:text-gray-300">
                All plans are billed monthly. You can cancel anytime, and you'll continue to have access 
                to your plan features until the end of your current billing period. We use secure payment 
                processing through LemonSqueezy.
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* CTA Section */}
      <div className="py-16 bg-blue-600 dark:bg-blue-700">
        <div className="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-bold text-white mb-4">
            Ready to Get Started?
          </h2>
          <p className="text-xl text-blue-100 mb-8">
            Join thousands of content creators, marketers, and professionals who trust GhostLayer 
            for their text humanization needs.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <button 
              onClick={() => document.getElementById('pricing-plans')?.scrollIntoView({ behavior: 'smooth' })}
              className="bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors"
            >
              View Plans
            </button>
            <a 
              href="/dashboard" 
              className="bg-blue-700 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-800 transition-colors border border-blue-500"
            >
              Try Free Version
            </a>
          </div>
        </div>
      </div>
    </div>
  )
}
