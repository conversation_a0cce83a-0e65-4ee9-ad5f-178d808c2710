import { NextRequest, NextResponse } from 'next/server'
import crypto from 'node:crypto'
import { configureLemonSqueezy } from '@/lib/lemonsqueezy'
import { getPrice } from '@lemonsqueezy/lemonsqueezy.js'

interface WebhookEvent {
  meta: {
    event_name: string
    custom_data: {
      user_id: string
    }
  }
  data: {
    id: string
    attributes: {
      user_name: string
      user_email: string
      status: string
      status_formatted: string
      renews_at: string
      ends_at: string
      trial_ends_at: string
      order_id: number
      variant_id: string
      first_subscription_item: {
        id: number
        price_id: string
        is_usage_based: boolean
      }
    }
  }
}

export async function POST(request: NextRequest) {
  try {
    if (!process.env.LEMONSQUEEZY_WEBHOOK_SECRET) {
      return NextResponse.json(
        { error: 'Webhook secret not configured' },
        { status: 500 }
      )
    }

    // Verify webhook signature
    const rawBody = await request.text()
    const secret = process.env.LEMONSQUEEZY_WEBHOOK_SECRET
    
    const hmac = crypto.createHmac('sha256', secret)
    const digest = Buffer.from(hmac.update(rawBody).digest('hex'), 'utf8')
    const signature = Buffer.from(
      request.headers.get('X-Signature') || '',
      'utf8'
    )

    if (!crypto.timingSafeEqual(digest, signature)) {
      console.error('Invalid webhook signature')
      return NextResponse.json(
        { error: 'Invalid signature' },
        { status: 401 }
      )
    }

    const event: WebhookEvent = JSON.parse(rawBody)
    
    console.log('Received webhook event:', event.meta.event_name)

    // Configure LemonSqueezy SDK
    configureLemonSqueezy()

    // Handle subscription events
    if (event.meta.event_name.startsWith('subscription_')) {
      await handleSubscriptionEvent(event)
    }

    return NextResponse.json({ received: true })
  } catch (error) {
    console.error('Webhook processing error:', error)
    return NextResponse.json(
      { error: 'Webhook processing failed' },
      { status: 500 }
    )
  }
}

async function handleSubscriptionEvent(event: WebhookEvent) {
  const { data, meta } = event
  const attributes = data.attributes
  
  try {
    // Get price information
    const priceId = attributes.first_subscription_item.price_id
    const priceData = await getPrice(priceId)
    
    if (priceData.error) {
      console.error('Failed to get price data:', priceData.error)
      return
    }

    const isUsageBased = attributes.first_subscription_item.is_usage_based
    const price = isUsageBased
      ? priceData.data?.data.attributes.unit_price_decimal
      : priceData.data?.data.attributes.unit_price

    const subscriptionData = {
      lemonSqueezyId: data.id,
      userId: meta.custom_data.user_id,
      orderId: attributes.order_id,
      name: attributes.user_name,
      email: attributes.user_email,
      status: attributes.status,
      statusFormatted: attributes.status_formatted,
      renewsAt: attributes.renews_at,
      endsAt: attributes.ends_at,
      trialEndsAt: attributes.trial_ends_at,
      variantId: attributes.variant_id,
      price: price?.toString() || '0',
      isUsageBased,
      subscriptionItemId: attributes.first_subscription_item.id,
    }

    // Here you would typically save to your database
    // For now, we'll just log the subscription data
    console.log('Subscription data:', subscriptionData)

    // You can implement database operations here
    // Example: await saveSubscriptionToDatabase(subscriptionData)

  } catch (error) {
    console.error('Error handling subscription event:', error)
    throw error
  }
}
