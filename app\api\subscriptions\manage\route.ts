import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth/next'
import { 
  cancelSubscription, 
  updateSubscription, 
  getSubscription 
} from '@lemonsqueezy/lemonsqueezy.js'
import { configureLemonSqueezy } from '@/lib/lemonsqueezy'
import { authOptions } from '@/app/api/auth/[...nextauth]/route'

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user) {
      return NextResponse.json(
        { error: 'User is not authenticated' },
        { status: 401 }
      )
    }

    const { subscriptionId, action } = await request.json()

    if (!subscriptionId || !action) {
      return NextResponse.json(
        { error: 'Subscription ID and action are required' },
        { status: 400 }
      )
    }

    // Configure LemonSqueezy SDK
    configureLemonSqueezy()

    let result

    switch (action) {
      case 'cancel':
        result = await cancelSubscription(subscriptionId)
        break
      
      case 'pause':
        result = await updateSubscription(subscriptionId, {
          pause: {
            mode: 'void',
          },
        })
        break
      
      case 'unpause':
        result = await updateSubscription(subscriptionId, {
          // @ts-expect-error -- null is a valid value for pause
          pause: null,
        })
        break
      
      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        )
    }

    if (result.error) {
      console.error(`LemonSqueezy ${action} error:`, result.error)
      return NextResponse.json(
        { error: `Failed to ${action} subscription` },
        { status: 500 }
      )
    }

    // Here you would typically update your database
    // For now, we'll just return success
    console.log(`Subscription ${action}ed successfully:`, result.data)

    return NextResponse.json({
      success: true,
      subscription: result.data,
    })
  } catch (error) {
    console.error('Subscription management error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
